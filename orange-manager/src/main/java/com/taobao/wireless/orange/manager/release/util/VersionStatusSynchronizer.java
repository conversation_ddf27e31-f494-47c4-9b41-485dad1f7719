package com.taobao.wireless.orange.manager.release.util;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.wireless.orange.common.constant.enums.ChangeType;
import com.taobao.wireless.orange.common.constant.enums.VersionStatus;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 版本状态同步工具类
 * 用于统一处理版本实体和主实体之间的状态同步逻辑
 */
public class VersionStatusSynchronizer {

    /**
     * 同步版本状态
     * 
     * @param versionEntities 版本实体列表
     * @param versionDAO 版本实体DAO
     * @param mainDAO 主实体DAO
     * @param releaseVersion 发布版本号
     * @param getEntityId 获取实体ID的函数
     * @param getChangeType 获取变更类型的函数
     * @param createVersionUpdateWrapper 创建版本实体更新条件的函数
     * @param createMainUpdateWrapper 创建主实体更新条件的函数
     * @param onlineStatus 上线状态
     * @param invalidStatus 无效状态
     * @param <V> 版本实体类型
     * @param <M> 主实体类型
     */
    public static <V, M> void synchronizeVersionStatus(
            List<V> versionEntities,
            IService<V> versionDAO,
            IService<M> mainDAO,
            String releaseVersion,
            Function<V, String> getEntityId,
            Function<V, ChangeType> getChangeType,
            Function<List<String>, LambdaUpdateWrapper<V>> createVersionUpdateWrapper,
            Function<List<String>, LambdaUpdateWrapper<M>> createMainUpdateWrapper,
            Object onlineStatus,
            Object invalidStatus) {

        if (CollectionUtils.isEmpty(versionEntities)) {
            return;
        }

        // 获取所有实体ID
        List<String> entityIds = versionEntities.stream()
                .map(getEntityId)
                .distinct()
                .collect(Collectors.toList());

        // 1. 将历史发布版本标记为过期
        LambdaUpdateWrapper<V> outdateWrapper = createVersionUpdateWrapper.apply(entityIds);
        outdateWrapper.eq("status", VersionStatus.RELEASED)
                .set("status", VersionStatus.OUTDATED);
        versionDAO.update(outdateWrapper);

        // 2. 将本次发布版本标记为发布
        LambdaUpdateWrapper<V> releaseWrapper = (LambdaUpdateWrapper<V>) versionDAO.lambdaUpdate()
                .eq("release_version", releaseVersion)
                .set("status", VersionStatus.RELEASED);
        versionDAO.update(releaseWrapper);

        // 3. 处理删除的实体
        List<String> deleteEntityIds = versionEntities.stream()
                .filter(v -> ChangeType.DELETE.equals(getChangeType.apply(v)))
                .map(getEntityId)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(deleteEntityIds)) {
            LambdaUpdateWrapper<M> deleteWrapper = createMainUpdateWrapper.apply(deleteEntityIds);
            deleteWrapper.set("status", invalidStatus);
            mainDAO.update(deleteWrapper);
        }

        // 4. 处理新增的实体
        List<String> newEntityIds = versionEntities.stream()
                .filter(v -> ChangeType.CREATE.equals(getChangeType.apply(v)))
                .map(getEntityId)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(newEntityIds)) {
            LambdaUpdateWrapper<M> onlineWrapper = createMainUpdateWrapper.apply(newEntityIds);
            onlineWrapper.set("status", onlineStatus);
            mainDAO.update(onlineWrapper);
        }
    }

    /**
     * 简化的参数条件版本状态同步
     * 
     * @param parameterConditionVersions 参数条件版本列表
     * @param versionDAO 版本DAO
     * @param releaseVersion 发布版本号
     * @param getParameterId 获取参数ID的函数
     * @param getConditionId 获取条件ID的函数
     * @param <V> 版本实体类型
     */
    public static <V> void synchronizeParameterConditionVersionStatus(
            List<V> parameterConditionVersions,
            IService<V> versionDAO,
            String releaseVersion,
            Function<V, String> getParameterId,
            Function<V, String> getConditionId) {

        if (CollectionUtils.isEmpty(parameterConditionVersions)) {
            return;
        }

        // 将历史发布版本标记为过期
        LambdaUpdateWrapper<V> updateChainWrapper = versionDAO.lambdaUpdate();
        for (V parameterCondition : parameterConditionVersions) {
            updateChainWrapper.or(i -> i
                    .eq("parameter_id", getParameterId.apply(parameterCondition))
                    .eq("condition_id", getConditionId.apply(parameterCondition))
                    .eq("status", VersionStatus.RELEASED));
        }
        updateChainWrapper.set("status", VersionStatus.OUTDATED);
        versionDAO.update(updateChainWrapper);

        // 将本次发布版本标记为发布
        LambdaUpdateWrapper<V> releaseWrapper = versionDAO.lambdaUpdate()
                .eq("release_version", releaseVersion)
                .set("status", VersionStatus.RELEASED);
        versionDAO.update(releaseWrapper);
    }
}
